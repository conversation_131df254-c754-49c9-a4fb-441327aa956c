# SQL Server Data Extraction Tool

A simple Python script to extract data from SQL Server databases.

## Prerequisites

1. Python 3.6 or higher
2. SQL Server ODBC Driver (Microsoft ODBC Driver 17 for SQL Server is recommended)
3. Required Python packages (install using `pip install -r requirements.txt`):
   - pyodbc
   - pandas

## Installation

1. Clone or download this repository
2. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Make sure you have the SQL Server ODBC driver installed on your system

## Usage

Run the script:
```
python sql_extract.py
```

The script will prompt you for:
1. SQL Server name
2. Database name
3. Authentication method (Windows or SQL Server)
4. SQL query (can be entered directly or loaded from a file)
5. Option to save results to a CSV file

## Example

```
Enter SQL Server name: SQLSERVER01
Enter database name: AdventureWorks
Use Windows Authentication? (Y/N): Y
Enter your SQL query (or type 'file' to load from a file): SELECT TOP 10 * FROM Person.Person
```

## Notes

- For Windows Authentication, make sure you're running the script with appropriate permissions
- For SQL Server Authentication, you'll need to provide a username and password
- The default ODBC driver is set to "ODBC Driver 17 for SQL Server" - modify the script if you have a different driver installed
