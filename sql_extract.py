import os
import sys
import csv
import pyodbc
import pandas as pd
from datetime import datetime

def connect_to_sql_server(server, database, username=None, password=None, trusted_connection=True):
    """
    Establish a connection to SQL Server.
    
    Args:
        server (str): SQL Server instance name
        database (str): Database name
        username (str, optional): SQL Server username for SQL authentication
        password (str, optional): SQL Server password for SQL authentication
        trusted_connection (bool): Whether to use Windows Authentication (True) or SQL Authentication (False)
    
    Returns:
        pyodbc.Connection: Database connection object
    """
    try:
        if trusted_connection:
            # Windows Authentication
            conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};Trusted_Connection=yes;'
        else:
            # SQL Server Authentication
            conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'
        
        conn = pyodbc.connect(conn_str)
        print(f"Successfully connected to database: {database} on server: {server}")
        return conn
    except Exception as e:
        print(f"Error connecting to database: {e}")
        sys.exit(1)

def execute_query(conn, query):
    """
    Execute a SQL query and return the results.
    
    Args:
        conn (pyodbc.Connection): Database connection object
        query (str): SQL query to execute
    
    Returns:
        pandas.DataFrame: Query results as a DataFrame
    """
    try:
        df = pd.read_sql(query, conn)
        print(f"Query executed successfully. Retrieved {len(df)} rows.")
        return df
    except Exception as e:
        print(f"Error executing query: {e}")
        sys.exit(1)

def save_to_csv(df, output_file=None):
    """
    Save DataFrame to a CSV file.
    
    Args:
        df (pandas.DataFrame): Data to save
        output_file (str, optional): Output file path. If None, generates a filename with timestamp.
    
    Returns:
        str: Path to the saved file
    """
    if output_file is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"sql_extract_{timestamp}.csv"
    
    try:
        df.to_csv(output_file, index=False)
        print(f"Data saved to {output_file}")
        return output_file
    except Exception as e:
        print(f"Error saving data to CSV: {e}")
        return None

def main():
    # Database connection parameters
    server = input("Enter SQL Server name: ")
    database = input("Enter database name: ")
    
    auth_type = input("Use Windows Authentication? (Y/N): ").strip().upper()
    if auth_type == 'Y':
        conn = connect_to_sql_server(server, database)
    else:
        username = input("Enter SQL Server username: ")
        password = input("Enter SQL Server password: ")
        conn = connect_to_sql_server(server, database, username, password, trusted_connection=False)
    
    # Get query from user
    query = input("Enter your SQL query (or type 'file' to load from a file): ")
    if query.lower() == 'file':
        query_file = input("Enter path to SQL file: ")
        with open(query_file, 'r') as f:
            query = f.read()
    
    # Execute query and get results
    df = execute_query(conn, query)
    
    # Display first few rows
    print("\nPreview of results:")
    print(df.head())
    
    # Save results
    save_option = input("Save results to CSV? (Y/N): ").strip().upper()
    if save_option == 'Y':
        custom_filename = input("Enter filename (or press Enter for auto-generated name): ")
        if custom_filename:
            save_to_csv(df, custom_filename)
        else:
            save_to_csv(df)
    
    # Close connection
    conn.close()
    print("Database connection closed.")

if __name__ == "__main__":
    main()
