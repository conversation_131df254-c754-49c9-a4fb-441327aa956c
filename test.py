import pyodbc
import pandas as pd

# Database connection parameters
server = 'HQPRCAD94BI03'
database = 'HERO'
username = 'PAAdmin'
password = 'PA@dmin'

# Connection string
connection_string = f'DRIVER={{SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'

try:
    # Establish connection
    connection = pyodbc.connect(connection_string)
    print("Connected to SQL Server successfully!")
    
    # Extract a table (replace 'your_table_name' with actual table name)
    query = "SELECT * FROM [HERO].[dbo].[checkupsSyncLog]"
    
    # Using pandas to read the data
    df = pd.read_sql(query, connection)
    
    print(f"Extracted {len(df)} rows from the table")
    print("\nFirst 5 rows:")
    print(df.head())
    
    # Optionally save to CSV
    # df.to_csv('extracted_data.csv', index=False)
    
except pyodbc.Error as e:
    print(f"Error connecting to database: {e}")
    
except Exception as e:
    print(f"An error occurred: {e}")
    
finally:
    # Close connection
    if 'connection' in locals():
        connection.close()
        print("Connection closed.")